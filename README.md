# Temporal.io 流程控制系统

基于Node.js + TypeScript + PostgreSQL + Temporal.io构建的全栈工作流管理系统。

## 🎯 项目概述

本项目是一个完整的流程控制解决方案，使用Temporal.io作为工作流引擎，提供可靠的、可扩展的业务流程管理能力。

### 核心特性

- ✅ **可靠的工作流执行**: 基于Temporal.io的分布式工作流引擎
- ✅ **流程控制**: 支持暂停、恢复、取消工作流
- ✅ **状态持久化**: PostgreSQL数据库存储流程状态和结果
- ✅ **RESTful API**: 完整的API接口用于流程管理
- ✅ **Web界面**: 简洁的前端界面进行流程操作
- ✅ **TypeScript支持**: 完整的类型安全
- ✅ **容器化部署**: Docker Compose一键启动

## 🏗️ 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端客户端     │    │   API服务器      │    │  Temporal Worker │
│  (HTML/JS)      │◄──►│   (Express)     │◄──►│   (工作流执行)    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │                        │
                              ▼                        ▼
                    ┌─────────────────┐    ┌─────────────────┐
                    │  PostgreSQL     │    │  Temporal Server│
                    │   (业务数据)     │    │   (工作流状态)   │
                    └─────────────────┘    └─────────────────┘
```

## 📁 项目结构

```
temporal-workflow-system/
├── src/
│   ├── activities/          # Temporal活动函数
│   │   └── index.ts
│   ├── workflows/           # Temporal工作流定义
│   │   └── businessProcess.ts
│   ├── database/            # 数据库连接和配置
│   │   └── connection.ts
│   ├── api/                 # REST API服务器
│   │   └── server.ts
│   ├── client/              # 前端客户端
│   │   └── index.html
│   └── worker.ts            # Temporal Worker
├── temporal-config/         # Temporal配置文件
├── docker-compose.yml       # Docker编排文件
├── tsconfig.json           # TypeScript配置
├── package.json            # 项目依赖
├── .env                    # 环境变量
└── README.md              # 项目文档
```

## 🚀 快速开始

### 前置要求

- Node.js 18+ 
- Docker & Docker Compose
- Git

### 1. 启动基础服务

使用Docker Compose启动PostgreSQL和Temporal服务：

```bash
# 启动数据库和Temporal服务
docker-compose up -d

# 查看服务状态
docker-compose ps
```

服务启动后可访问：
- Temporal Web UI: http://localhost:8088
- PostgreSQL: localhost:5432

### 2. 安装依赖

```bash
# 安装项目依赖
npm install

# 编译TypeScript
npm run build
```

### 3. 启动应用

#### 开发模式（推荐）

```bash
# 同时启动Worker和API服务器
npm run dev
```

#### 生产模式

```bash
# 编译并启动
npm start
```

#### 分别启动

```bash
# 终端1: 启动Temporal Worker
npm run dev:worker

# 终端2: 启动API服务器  
npm run dev:server

# 终端3: 启动前端客户端
npm run client
```

### 4. 访问应用

- **前端界面**: http://localhost:8080
- **API服务**: http://localhost:3000
- **健康检查**: http://localhost:3000/health
- **Temporal Web**: http://localhost:8088

## 📖 使用指南

### 启动新的业务流程

1. 打开前端界面 http://localhost:8080
2. 填写流程名称和数据（JSON格式）
3. 点击"启动流程"按钮
4. 记录返回的工作流ID

### 流程控制操作

使用工作流ID可以进行以下操作：

- **查询状态**: 获取当前流程执行状态
- **暂停流程**: 暂停正在执行的流程
- **恢复流程**: 恢复已暂停的流程  
- **取消流程**: 取消流程执行
- **获取结果**: 获取流程执行结果

### API接口

#### 启动工作流
```bash
POST /api/workflows/start
Content-Type: application/json

{
  "processName": "示例流程",
  "data": {
    "orderId": "12345",
    "amount": 1000
  },
  "config": {
    "enableNotifications": true
  }
}
```

#### 查询状态
```bash
GET /api/workflows/{workflowId}/status
```

#### 控制操作
```bash
POST /api/workflows/{workflowId}/pause   # 暂停
POST /api/workflows/{workflowId}/resume  # 恢复  
POST /api/workflows/{workflowId}/cancel  # 取消
```

## 🔧 配置说明

### 环境变量 (.env)

```bash
# 数据库配置
DB_HOST=localhost
DB_PORT=5432
DB_NAME=temporal_workflow_db
DB_USER=postgres
DB_PASSWORD=1qaz@WSX

# Temporal配置
TEMPORAL_ADDRESS=localhost:7233
TEMPORAL_NAMESPACE=default

# 服务器配置
PORT=3000
NODE_ENV=development
```

### 数据库表结构

系统会自动创建以下数据表：

1. **workflow_executions**: 工作流执行记录
2. **business_processes**: 业务流程数据

## 🔄 工作流示例

系统包含一个完整的业务流程示例，包含三个步骤：

1. **数据预处理**: 验证和清理输入数据
2. **核心业务逻辑**: 执行主要业务逻辑
3. **结果处理**: 验证和保存最终结果

每个步骤都支持：
- 错误重试
- 状态持久化
- 进度跟踪
- 暂停/恢复

## 🛠️ 开发指南

### 添加新的工作流

1. 在 `src/workflows/` 目录创建新的工作流文件
2. 在 `src/activities/` 目录添加对应的活动函数
3. 更新Worker配置以包含新的工作流

### 扩展API接口

1. 在 `src/api/server.ts` 中添加新的路由
2. 实现对应的业务逻辑
3. 更新前端界面（如需要）

### 数据库操作

使用 `src/database/connection.ts` 中的连接池进行数据库操作：

```typescript
import pool from '../database/connection';

const client = await pool.connect();
const result = await client.query('SELECT * FROM business_processes');
client.release();
```

## 🐛 故障排除

### 常见问题

1. **Temporal连接失败**
   - 确保Docker服务正在运行
   - 检查端口7233是否被占用

2. **数据库连接失败**  
   - 验证PostgreSQL服务状态
   - 检查数据库凭据配置

3. **Worker启动失败**
   - 确保Temporal服务已启动
   - 检查任务队列配置

### 日志查看

```bash
# 查看Docker服务日志
docker-compose logs temporal
docker-compose logs postgres

# 查看应用日志
npm run dev:worker  # Worker日志
npm run dev:server  # API服务器日志
```

## 📊 监控和管理

### Temporal Web UI

访问 http://localhost:8088 可以：

- 查看所有工作流执行历史
- 监控工作流性能指标
- 调试工作流执行问题
- 管理命名空间和任务队列

### 数据库管理

可以使用任何PostgreSQL客户端连接到数据库：

```
Host: localhost
Port: 5432
Database: temporal_workflow_db
Username: postgres
Password: 1qaz@WSX
```

## 🚀 部署指南

### Docker部署

```bash
# 构建生产镜像
docker build -t temporal-workflow-system .

# 使用Docker Compose部署
docker-compose -f docker-compose.prod.yml up -d
```

### 环境配置

生产环境需要修改以下配置：

1. 更新数据库密码
2. 配置SSL证书
3. 设置环境变量
4. 配置负载均衡

## 🤝 贡献指南

1. Fork项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建Pull Request

## 📄 许可证

本项目采用ISC许可证。

## 📞 支持

如有问题或建议，请：

1. 查看文档和FAQ
2. 搜索已有Issues
3. 创建新的Issue
4. 联系项目维护者

---

**项目创建时间**: 2025年7月1日  
**技术栈**: Node.js + TypeScript + Temporal.io + PostgreSQL + Express + Docker
