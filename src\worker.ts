import { Worker } from '@temporalio/worker';
import * as activities from './activities';
import { testConnection, initDatabase } from './database/connection';
import dotenv from 'dotenv';

dotenv.config();

/**
 * Temporal Worker
 * 负责执行工作流和活动函数
 */
async function run() {
  console.log('🚀 启动Temporal Worker...');

  // 测试数据库连接
  const dbConnected = await testConnection();
  if (!dbConnected) {
    console.error('❌ 数据库连接失败，Worker启动中止');
    process.exit(1);
  }

  // 初始化数据库
  try {
    await initDatabase();
  } catch (error) {
    console.error('❌ 数据库初始化失败:', error);
    process.exit(1);
  }

  // 创建Worker实例
  const worker = await Worker.create({
    workflowsPath: require.resolve('./workflows'),
    activities,
    taskQueue: 'business-process-queue',
    // Worker配置
    maxConcurrentActivityTaskExecutions: 10,
    maxConcurrentWorkflowTaskExecutions: 10,
  });

  console.log('✅ Temporal Worker已启动');
  console.log('📋 任务队列: business-process-queue');
  console.log('🔄 等待工作流任务...');

  // 优雅关闭处理
  process.on('SIGINT', async () => {
    console.log('\n🛑 收到关闭信号，正在优雅关闭Worker...');
    await worker.shutdown();
    console.log('✅ Worker已关闭');
    process.exit(0);
  });

  process.on('SIGTERM', async () => {
    console.log('\n🛑 收到终止信号，正在优雅关闭Worker...');
    await worker.shutdown();
    console.log('✅ Worker已关闭');
    process.exit(0);
  });

  // 运行Worker
  await worker.run();
}

// 启动Worker
run().catch((err) => {
  console.error('❌ Worker启动失败:', err);
  process.exit(1);
});
