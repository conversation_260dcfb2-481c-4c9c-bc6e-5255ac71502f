# Temporal.io 流程控制系统启动脚本
# PowerShell脚本用于Windows环境

Write-Host "🚀 启动Temporal.io流程控制系统..." -ForegroundColor Green

# 检查Docker是否运行
Write-Host "📋 检查Docker服务状态..." -ForegroundColor Yellow
try {
    docker version | Out-Null
    Write-Host "✅ Docker服务正常运行" -ForegroundColor Green
} catch {
    Write-Host "❌ Docker服务未运行，请先启动Docker Desktop" -ForegroundColor Red
    exit 1
}

# 检查Node.js是否安装
Write-Host "📋 检查Node.js环境..." -ForegroundColor Yellow
try {
    $nodeVersion = node --version
    Write-Host "✅ Node.js版本: $nodeVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Node.js未安装，请先安装Node.js 18+" -ForegroundColor Red
    exit 1
}

# 启动Docker服务
Write-Host "🐳 启动Docker服务..." -ForegroundColor Yellow
docker-compose up -d

# 等待服务启动
Write-Host "⏳ 等待服务启动完成..." -ForegroundColor Yellow
Start-Sleep -Seconds 10

# 检查服务状态
Write-Host "📋 检查服务状态..." -ForegroundColor Yellow
docker-compose ps

# 安装依赖（如果需要）
if (-not (Test-Path "node_modules")) {
    Write-Host "📦 安装项目依赖..." -ForegroundColor Yellow
    npm install
}

# 编译TypeScript
Write-Host "🔨 编译TypeScript..." -ForegroundColor Yellow
npm run build

Write-Host "🎉 系统启动完成！" -ForegroundColor Green
Write-Host ""
Write-Host "📊 服务地址:" -ForegroundColor Cyan
Write-Host "  - 前端界面: http://localhost:8080" -ForegroundColor White
Write-Host "  - API服务: http://localhost:3000" -ForegroundColor White  
Write-Host "  - 健康检查: http://localhost:3000/health" -ForegroundColor White
Write-Host "  - Temporal Web: http://localhost:8088" -ForegroundColor White
Write-Host ""
Write-Host "🚀 启动应用服务..." -ForegroundColor Green
Write-Host "请在新的终端窗口中运行以下命令:" -ForegroundColor Yellow
Write-Host "  npm run dev" -ForegroundColor White
Write-Host ""
Write-Host "或者分别启动:" -ForegroundColor Yellow  
Write-Host "  npm run dev:worker  # 启动Worker" -ForegroundColor White
Write-Host "  npm run dev:server  # 启动API服务器" -ForegroundColor White
Write-Host "  npm run client      # 启动前端客户端" -ForegroundColor White
