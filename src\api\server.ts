import express, { Request, Response } from 'express';
import cors from 'cors';
import { Client, Connection } from '@temporalio/client';
import { businessProcessWorkflow, pauseSignal, resumeSignal, cancelSignal, statusQuery } from '../workflows/businessProcess';
import { testConnection } from '../database/connection';
import pool from '../database/connection';
import dotenv from 'dotenv';

dotenv.config();

const app = express();
const port = process.env.PORT || 3000;

// 中间件
app.use(cors());
app.use(express.json());

// Temporal客户端
let temporalClient: Client;

// 初始化Temporal客户端
async function initTemporalClient() {
  try {
    const connection = await Connection.connect({
      address: process.env.TEMPORAL_ADDRESS || 'localhost:7233',
    });

    temporalClient = new Client({
      connection,
      namespace: process.env.TEMPORAL_NAMESPACE || 'default',
    });
    console.log('✅ Temporal客户端连接成功');
  } catch (error) {
    console.error('❌ Temporal客户端连接失败:', error);
    throw error;
  }
}

// 健康检查端点
app.get('/health', async (req: Request, res: Response) => {
  const dbStatus = await testConnection();
  res.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    database: dbStatus ? 'connected' : 'disconnected',
    temporal: temporalClient ? 'connected' : 'disconnected'
  });
});

// 启动新的业务流程
app.post('/api/workflows/start', async (req: Request, res: Response) => {
  try {
    const { processName, data, config } = req.body;
    
    if (!processName || !data) {
      return res.status(400).json({
        error: '缺少必要参数: processName 和 data'
      });
    }

    const processId = `process_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    // 保存到数据库
    const client = await pool.connect();
    await client.query(
      'INSERT INTO business_processes (process_name, process_data, workflow_id) VALUES ($1, $2, $3)',
      [processName, JSON.stringify(data), processId]
    );
    client.release();

    // 启动工作流
    const handle = await temporalClient.workflow.start(businessProcessWorkflow, {
      taskQueue: 'business-process-queue',
      workflowId: processId,
      args: [{
        processId,
        processName,
        data,
        config: config || {}
      }],
    });

    res.json({
      success: true,
      processId,
      workflowId: handle.workflowId,
      runId: handle.firstExecutionRunId,
      message: '工作流已启动'
    });

  } catch (error) {
    console.error('启动工作流失败:', error);
    res.status(500).json({
      error: '启动工作流失败',
      details: error instanceof Error ? error.message : '未知错误'
    });
  }
});

// 查询工作流状态
app.get('/api/workflows/:workflowId/status', async (req: Request, res: Response) => {
  try {
    const { workflowId } = req.params;

    const handle = temporalClient.workflow.getHandle(workflowId);
    const status = await handle.query(statusQuery);
    const description = await handle.describe();

    res.json({
      workflowId,
      status,
      workflowStatus: description.status.name,
      runId: description.runId,
      startTime: description.startTime,
      executionTime: description.executionTime
    });

  } catch (error) {
    console.error('查询工作流状态失败:', error);
    res.status(500).json({
      error: '查询工作流状态失败',
      details: error instanceof Error ? error.message : '未知错误'
    });
  }
});

// 暂停工作流
app.post('/api/workflows/:workflowId/pause', async (req: Request, res: Response) => {
  try {
    const { workflowId } = req.params;

    const handle = temporalClient.workflow.getHandle(workflowId);
    await handle.signal(pauseSignal);

    res.json({
      success: true,
      message: '工作流已暂停'
    });

  } catch (error) {
    console.error('暂停工作流失败:', error);
    res.status(500).json({
      error: '暂停工作流失败',
      details: error instanceof Error ? error.message : '未知错误'
    });
  }
});

// 恢复工作流
app.post('/api/workflows/:workflowId/resume', async (req: Request, res: Response) => {
  try {
    const { workflowId } = req.params;

    const handle = temporalClient.workflow.getHandle(workflowId);
    await handle.signal(resumeSignal);

    res.json({
      success: true,
      message: '工作流已恢复'
    });

  } catch (error) {
    console.error('恢复工作流失败:', error);
    res.status(500).json({
      error: '恢复工作流失败',
      details: error instanceof Error ? error.message : '未知错误'
    });
  }
});

// 取消工作流
app.post('/api/workflows/:workflowId/cancel', async (req: Request, res: Response) => {
  try {
    const { workflowId } = req.params;

    const handle = temporalClient.workflow.getHandle(workflowId);
    await handle.signal(cancelSignal);

    res.json({
      success: true,
      message: '工作流已取消'
    });

  } catch (error) {
    console.error('取消工作流失败:', error);
    res.status(500).json({
      error: '取消工作流失败',
      details: error instanceof Error ? error.message : '未知错误'
    });
  }
});

// 获取工作流结果
app.get('/api/workflows/:workflowId/result', async (req: Request, res: Response) => {
  try {
    const { workflowId } = req.params;

    const handle = temporalClient.workflow.getHandle(workflowId);
    const result = await handle.result();

    res.json({
      success: true,
      result
    });

  } catch (error) {
    console.error('获取工作流结果失败:', error);
    res.status(500).json({
      error: '获取工作流结果失败',
      details: error instanceof Error ? error.message : '未知错误'
    });
  }
});

// 获取所有业务流程列表
app.get('/api/processes', async (req: Request, res: Response) => {
  try {
    const client = await pool.connect();
    const result = await client.query(
      'SELECT * FROM business_processes ORDER BY created_at DESC LIMIT 50'
    );
    client.release();

    res.json({
      success: true,
      processes: result.rows
    });

  } catch (error) {
    console.error('获取流程列表失败:', error);
    res.status(500).json({
      error: '获取流程列表失败',
      details: error instanceof Error ? error.message : '未知错误'
    });
  }
});

// 启动服务器
async function startServer() {
  try {
    // 初始化Temporal客户端
    await initTemporalClient();
    
    // 测试数据库连接
    const dbConnected = await testConnection();
    if (!dbConnected) {
      throw new Error('数据库连接失败');
    }

    app.listen(port, () => {
      console.log(`🚀 API服务器已启动`);
      console.log(`📡 服务地址: http://localhost:${port}`);
      console.log(`🏥 健康检查: http://localhost:${port}/health`);
    });

  } catch (error) {
    console.error('❌ 服务器启动失败:', error);
    process.exit(1);
  }
}

// 优雅关闭
process.on('SIGINT', () => {
  console.log('\n🛑 收到关闭信号，正在关闭服务器...');
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\n🛑 收到终止信号，正在关闭服务器...');
  process.exit(0);
});

startServer();
