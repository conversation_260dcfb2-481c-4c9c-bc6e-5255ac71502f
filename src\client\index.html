<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Temporal.io 流程控制系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f5f5;
            color: #333;
            line-height: 1.6;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            border-radius: 10px;
            margin-bottom: 2rem;
            text-align: center;
        }
        
        .card {
            background: white;
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .form-group {
            margin-bottom: 1rem;
        }
        
        label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
        }
        
        input, textarea, select {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 1rem;
        }
        
        button {
            background: #667eea;
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 5px;
            cursor: pointer;
            font-size: 1rem;
            margin-right: 0.5rem;
            margin-bottom: 0.5rem;
        }
        
        button:hover {
            background: #5a6fd8;
        }
        
        button.secondary {
            background: #6c757d;
        }
        
        button.danger {
            background: #dc3545;
        }
        
        .status {
            padding: 0.5rem 1rem;
            border-radius: 5px;
            margin: 0.5rem 0;
            font-weight: 600;
        }
        
        .status.running { background: #d4edda; color: #155724; }
        .status.completed { background: #cce7ff; color: #004085; }
        .status.failed { background: #f8d7da; color: #721c24; }
        .status.paused { background: #fff3cd; color: #856404; }
        
        .workflow-item {
            border: 1px solid #eee;
            border-radius: 5px;
            padding: 1rem;
            margin-bottom: 1rem;
        }
        
        .workflow-controls {
            margin-top: 1rem;
        }
        
        #result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 1rem;
            margin-top: 1rem;
            white-space: pre-wrap;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔄 Temporal.io 流程控制系统</h1>
            <p>基于Node.js + PostgreSQL + Temporal.io的工作流管理平台</p>
        </div>

        <!-- 启动新流程 -->
        <div class="card">
            <h2>🚀 启动新的业务流程</h2>
            <form id="startWorkflowForm">
                <div class="form-group">
                    <label for="processName">流程名称:</label>
                    <input type="text" id="processName" value="示例业务流程" required>
                </div>
                
                <div class="form-group">
                    <label for="processData">流程数据 (JSON格式):</label>
                    <textarea id="processData" rows="4" placeholder='{"orderId": "12345", "amount": 1000, "customer": "张三"}'></textarea>
                </div>
                
                <div class="form-group">
                    <label>
                        <input type="checkbox" id="enableNotifications"> 启用通知
                    </label>
                </div>
                
                <button type="submit">启动流程</button>
            </form>
        </div>

        <!-- 流程控制 -->
        <div class="card">
            <h2>🎮 流程控制</h2>
            <div class="form-group">
                <label for="workflowId">工作流ID:</label>
                <input type="text" id="workflowId" placeholder="输入工作流ID">
            </div>
            
            <div class="workflow-controls">
                <button onclick="queryStatus()">查询状态</button>
                <button onclick="pauseWorkflow()" class="secondary">暂停</button>
                <button onclick="resumeWorkflow()">恢复</button>
                <button onclick="cancelWorkflow()" class="danger">取消</button>
                <button onclick="getResult()">获取结果</button>
            </div>
        </div>

        <!-- 流程列表 -->
        <div class="card">
            <h2>📋 流程列表</h2>
            <button onclick="loadProcesses()">刷新列表</button>
            <div id="processList"></div>
        </div>

        <!-- 结果显示 -->
        <div class="card">
            <h2>📊 操作结果</h2>
            <div id="result">等待操作...</div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:3000/api';
        
        // 显示结果
        function showResult(data) {
            document.getElementById('result').textContent = JSON.stringify(data, null, 2);
        }
        
        // 启动工作流
        document.getElementById('startWorkflowForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const processName = document.getElementById('processName').value;
            const processDataText = document.getElementById('processData').value;
            const enableNotifications = document.getElementById('enableNotifications').checked;
            
            let processData;
            try {
                processData = processDataText ? JSON.parse(processDataText) : {};
            } catch (error) {
                alert('流程数据格式错误，请输入有效的JSON');
                return;
            }
            
            try {
                const response = await fetch(`${API_BASE}/workflows/start`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        processName,
                        data: processData,
                        config: { enableNotifications }
                    })
                });
                
                const result = await response.json();
                showResult(result);
                
                if (result.success) {
                    document.getElementById('workflowId').value = result.workflowId;
                }
            } catch (error) {
                showResult({ error: '请求失败', details: error.message });
            }
        });
        
        // 查询状态
        async function queryStatus() {
            const workflowId = document.getElementById('workflowId').value;
            if (!workflowId) {
                alert('请输入工作流ID');
                return;
            }
            
            try {
                const response = await fetch(`${API_BASE}/workflows/${workflowId}/status`);
                const result = await response.json();
                showResult(result);
            } catch (error) {
                showResult({ error: '查询失败', details: error.message });
            }
        }
        
        // 暂停工作流
        async function pauseWorkflow() {
            const workflowId = document.getElementById('workflowId').value;
            if (!workflowId) {
                alert('请输入工作流ID');
                return;
            }
            
            try {
                const response = await fetch(`${API_BASE}/workflows/${workflowId}/pause`, {
                    method: 'POST'
                });
                const result = await response.json();
                showResult(result);
            } catch (error) {
                showResult({ error: '暂停失败', details: error.message });
            }
        }
        
        // 恢复工作流
        async function resumeWorkflow() {
            const workflowId = document.getElementById('workflowId').value;
            if (!workflowId) {
                alert('请输入工作流ID');
                return;
            }
            
            try {
                const response = await fetch(`${API_BASE}/workflows/${workflowId}/resume`, {
                    method: 'POST'
                });
                const result = await response.json();
                showResult(result);
            } catch (error) {
                showResult({ error: '恢复失败', details: error.message });
            }
        }
        
        // 取消工作流
        async function cancelWorkflow() {
            const workflowId = document.getElementById('workflowId').value;
            if (!workflowId) {
                alert('请输入工作流ID');
                return;
            }
            
            if (!confirm('确定要取消这个工作流吗？')) {
                return;
            }
            
            try {
                const response = await fetch(`${API_BASE}/workflows/${workflowId}/cancel`, {
                    method: 'POST'
                });
                const result = await response.json();
                showResult(result);
            } catch (error) {
                showResult({ error: '取消失败', details: error.message });
            }
        }
        
        // 获取结果
        async function getResult() {
            const workflowId = document.getElementById('workflowId').value;
            if (!workflowId) {
                alert('请输入工作流ID');
                return;
            }
            
            try {
                const response = await fetch(`${API_BASE}/workflows/${workflowId}/result`);
                const result = await response.json();
                showResult(result);
            } catch (error) {
                showResult({ error: '获取结果失败', details: error.message });
            }
        }
        
        // 加载流程列表
        async function loadProcesses() {
            try {
                const response = await fetch(`${API_BASE}/processes`);
                const result = await response.json();
                
                const listContainer = document.getElementById('processList');
                if (result.success && result.processes) {
                    listContainer.innerHTML = result.processes.map(process => `
                        <div class="workflow-item">
                            <h4>${process.process_name}</h4>
                            <p><strong>ID:</strong> ${process.workflow_id}</p>
                            <p><strong>状态:</strong> <span class="status ${process.status}">${process.status}</span></p>
                            <p><strong>创建时间:</strong> ${new Date(process.created_at).toLocaleString()}</p>
                            <button onclick="document.getElementById('workflowId').value='${process.workflow_id}'">选择此流程</button>
                        </div>
                    `).join('');
                } else {
                    listContainer.innerHTML = '<p>暂无流程数据</p>';
                }
            } catch (error) {
                document.getElementById('processList').innerHTML = `<p>加载失败: ${error.message}</p>`;
            }
        }
        
        // 页面加载时自动加载流程列表
        window.addEventListener('load', loadProcesses);
    </script>
</body>
</html>
