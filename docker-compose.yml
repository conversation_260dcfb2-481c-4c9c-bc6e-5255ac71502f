version: '3.8'

services:
  # PostgreSQL数据库
  postgres:
    image: postgres:15
    container_name: temporal_postgres
    environment:
      POSTGRES_DB: temporal_workflow_db
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: 1qaz@WSX
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - temporal-network

  # Temporal Server
  temporal:
    image: temporalio/auto-setup:1.22.0
    container_name: temporal_server
    depends_on:
      - postgres
    environment:
      - DB=postgresql
      - DB_PORT=5432
      - POSTGRES_USER=postgres
      - POSTGRES_PWD=1qaz@WSX
      - POSTGRES_SEEDS=postgres
      - DYNAMIC_CONFIG_FILE_PATH=config/dynamicconfig/development-sql.yaml
    ports:
      - "7233:7233"
    volumes:
      - ./temporal-config:/etc/temporal/config/dynamicconfig
    networks:
      - temporal-network

  # Temporal Web UI
  temporal-web:
    image: temporalio/web:2.21.3
    container_name: temporal_web
    depends_on:
      - temporal
    environment:
      - TEMPORAL_ADDRESS=temporal:7233
      - TEMPORAL_CORS_ORIGINS=http://localhost:3000,http://localhost:8080
    ports:
      - "8088:8088"
    networks:
      - temporal-network

volumes:
  postgres_data:

networks:
  temporal-network:
    driver: bridge
