import { Pool } from 'pg';
import dotenv from 'dotenv';

dotenv.config();

// PostgreSQL连接池配置
const pool = new Pool({
  host: process.env.DB_HOST,
  port: parseInt(process.env.DB_PORT || '5432'),
  database: process.env.DB_NAME,
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  max: 20, // 最大连接数
  idleTimeoutMillis: 30000,
  connectionTimeoutMillis: 2000,
});

// 测试数据库连接
export const testConnection = async (): Promise<boolean> => {
  try {
    const client = await pool.connect();
    await client.query('SELECT NOW()');
    client.release();
    console.log('✅ 数据库连接成功');
    return true;
  } catch (error) {
    console.error('❌ 数据库连接失败:', error);
    return false;
  }
};

// 初始化数据库表
export const initDatabase = async (): Promise<void> => {
  try {
    const client = await pool.connect();
    
    // 创建工作流执行记录表
    await client.query(`
      CREATE TABLE IF NOT EXISTS workflow_executions (
        id SERIAL PRIMARY KEY,
        workflow_id VARCHAR(255) NOT NULL,
        run_id VARCHAR(255) NOT NULL,
        workflow_type VARCHAR(255) NOT NULL,
        status VARCHAR(50) NOT NULL,
        input JSONB,
        result JSONB,
        error_message TEXT,
        started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        completed_at TIMESTAMP,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
    `);

    // 创建业务数据表示例
    await client.query(`
      CREATE TABLE IF NOT EXISTS business_processes (
        id SERIAL PRIMARY KEY,
        process_name VARCHAR(255) NOT NULL,
        process_data JSONB NOT NULL,
        status VARCHAR(50) NOT NULL DEFAULT 'pending',
        workflow_id VARCHAR(255),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
    `);

    client.release();
    console.log('✅ 数据库表初始化完成');
  } catch (error) {
    console.error('❌ 数据库初始化失败:', error);
    throw error;
  }
};

export default pool;
