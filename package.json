{"name": "temporal-workflow-system", "version": "1.0.0", "description": "基于Temporal.io的Node.js全栈流程控制系统", "main": "dist/api/server.js", "scripts": {"build": "tsc", "dev:worker": "ts-node src/worker.ts", "dev:server": "ts-node src/api/server.ts", "dev": "concurrently \"npm run dev:worker\" \"npm run dev:server\"", "start:worker": "node dist/worker.js", "start:server": "node dist/api/server.js", "start": "npm run build && concurrently \"npm run start:worker\" \"npm run start:server\"", "client": "cd src/client && python -m http.server 8080", "test:workflow": "ts-node src/test-workflow.ts", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["temporal", "workflow", "nodejs", "postgresql", "typescript"], "author": "", "license": "ISC", "dependencies": {"@temporalio/activity": "^1.11.8", "@temporalio/client": "^1.11.8", "@temporalio/worker": "^1.11.8", "@temporalio/workflow": "^1.11.8", "@types/express": "^4.17.0", "cors": "^2.8.5", "dotenv": "^17.0.0", "express": "^4.18.0", "pg": "^8.16.3"}, "devDependencies": {"@types/cors": "^2.8.19", "@types/node": "^24.0.8", "@types/pg": "^8.15.4", "concurrently": "^9.2.0", "nodemon": "^3.1.10", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}