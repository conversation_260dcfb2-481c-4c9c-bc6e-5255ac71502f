import { 
  proxyActivities, 
  sleep, 
  log,
  defineSignal,
  defineQuery,
  setHandler,
  condition,
  CancellationScope
} from '@temporalio/workflow';
import type * as activities from '../activities';

// 代理活动函数
const { 
  processStep1, 
  processStep2, 
  processStep3, 
  saveProcessResult,
  sendNotification 
} = proxyActivities<typeof activities>({
  startToCloseTimeout: '1 minute',
  retry: {
    initialInterval: '1s',
    maximumInterval: '30s',
    maximumAttempts: 3,
  },
});

// 定义信号和查询
export const pauseSignal = defineSignal<[]>('pause');
export const resumeSignal = defineSignal<[]>('resume');
export const cancelSignal = defineSignal<[]>('cancel');
export const statusQuery = defineQuery<string>('status');

// 工作流输入参数接口
export interface BusinessProcessInput {
  processId: string;
  processName: string;
  data: any;
  config?: {
    enableNotifications?: boolean;
    retryOnFailure?: boolean;
  };
}

// 工作流结果接口
export interface BusinessProcessResult {
  processId: string;
  status: 'completed' | 'failed' | 'cancelled';
  result?: any;
  error?: string;
  executionTime: number;
}

/**
 * 业务流程工作流
 * 这是一个示例工作流，展示了如何使用Temporal.io进行流程控制
 */
export async function businessProcessWorkflow(
  input: BusinessProcessInput
): Promise<BusinessProcessResult> {
  const startTime = Date.now();
  let currentStatus = 'running';
  let isPaused = false;
  let isCancelled = false;

  // 设置信号处理器
  setHandler(pauseSignal, () => {
    isPaused = true;
    currentStatus = 'paused';
    log.info('工作流已暂停', { processId: input.processId });
  });

  setHandler(resumeSignal, () => {
    isPaused = false;
    currentStatus = 'running';
    log.info('工作流已恢复', { processId: input.processId });
  });

  setHandler(cancelSignal, () => {
    isCancelled = true;
    currentStatus = 'cancelled';
    log.info('工作流已取消', { processId: input.processId });
  });

  setHandler(statusQuery, () => currentStatus);

  log.info('开始执行业务流程', { 
    processId: input.processId, 
    processName: input.processName 
  });

  try {
    // 检查暂停状态
    const waitForResume = async () => {
      await condition(() => !isPaused || isCancelled);
    };

    // 检查取消状态
    if (isCancelled) {
      return {
        processId: input.processId,
        status: 'cancelled',
        executionTime: Date.now() - startTime
      };
    }

    // 步骤1：数据预处理
    await waitForResume();
    if (isCancelled) throw new Error('工作流已取消');
    
    log.info('执行步骤1：数据预处理', { processId: input.processId });
    const step1Result = await processStep1(input.processId, input.data);
    
    // 步骤2：核心业务逻辑处理
    await waitForResume();
    if (isCancelled) throw new Error('工作流已取消');
    
    log.info('执行步骤2：核心业务逻辑处理', { processId: input.processId });
    const step2Result = await processStep2(input.processId, step1Result);
    
    // 可选的延迟（模拟长时间运行的任务）
    await sleep('5s');
    
    // 步骤3：结果处理和验证
    await waitForResume();
    if (isCancelled) throw new Error('工作流已取消');
    
    log.info('执行步骤3：结果处理和验证', { processId: input.processId });
    const step3Result = await processStep3(input.processId, step2Result);
    
    // 保存最终结果
    await saveProcessResult(input.processId, step3Result);
    
    // 发送通知（如果启用）
    if (input.config?.enableNotifications) {
      await sendNotification(input.processId, '流程执行完成', step3Result);
    }

    currentStatus = 'completed';
    const result: BusinessProcessResult = {
      processId: input.processId,
      status: 'completed',
      result: step3Result,
      executionTime: Date.now() - startTime
    };

    log.info('业务流程执行完成', { result });
    return result;

  } catch (error) {
    currentStatus = 'failed';
    const errorMessage = error instanceof Error ? error.message : '未知错误';
    
    log.error('业务流程执行失败', { 
      processId: input.processId, 
      error: errorMessage 
    });

    // 如果配置了失败重试，可以在这里添加重试逻辑
    if (input.config?.retryOnFailure && !isCancelled) {
      log.info('尝试重试流程', { processId: input.processId });
      // 这里可以添加重试逻辑
    }

    return {
      processId: input.processId,
      status: isCancelled ? 'cancelled' : 'failed',
      error: errorMessage,
      executionTime: Date.now() - startTime
    };
  }
}
